# PAMS 数据库管理指南

本文档介绍如何清除和重置PAMS数据库中的所有数据。

## 数据库文件位置

PAMS使用SQLite数据库，文件位置根据操作系统不同：

- **Linux**: `~/.config/pams/pams.db`
- **macOS**: `~/Library/Application Support/pams/pams.db`
- **Windows**: `%APPDATA%\pams\pams.db`

## 清除数据的方法

### 方法1：使用命令行脚本（推荐）

最简单的方法是使用提供的npm脚本：

```bash
# 停止PAMS应用（如果正在运行）
# 然后运行清除命令
npm run clear-db
```

这个命令会：
- 删除数据库文件
- 清理用户数据目录下的其他相关文件
- 下次启动PAMS时会自动创建新的数据库

### 方法2：手动删除数据库文件

1. **关闭PAMS应用**
2. **找到数据库文件位置**（见上面的路径）
3. **删除pams.db文件**

```bash
# Linux/macOS
rm ~/.config/pams/pams.db

# Windows (在PowerShell中)
Remove-Item "$env:APPDATA\pams\pams.db"
```

### 方法3：在应用内使用数据库管理功能

如果你能正常启动PAMS应用，可以使用内置的数据库管理功能：

1. 启动PAMS应用
2. 导航到"数据库管理"页面
3. 选择相应的清理选项：
   - **清空所有数据**：删除所有表中的数据，但保留表结构
   - **完全重置数据库**：删除数据库文件并重新创建

## 清理选项说明

### 清空所有数据
- 删除所有表中的数据
- 保留数据库表结构
- 重新创建默认管理员账户（admin/admin123）
- 适合需要保留配置但清空数据的情况

### 完全重置数据库
- 删除整个数据库文件
- 下次启动时重新创建所有表和默认数据
- 恢复到初始安装状态
- 适合完全重新开始的情况

## 注意事项

⚠️ **重要警告**：
- 所有清理操作都是**不可逆**的
- 清理前请确保已备份重要数据
- 执行清理操作前请关闭PAMS应用

## 默认账户信息

清理数据后，系统会自动创建默认管理员账户：
- **用户名**: admin
- **密码**: admin123
- **邮箱**: <EMAIL>

## 故障排除

### 数据库文件被锁定
如果遇到"数据库文件被锁定"的错误：
1. 确保PAMS应用已完全关闭
2. 检查是否有其他PAMS进程在运行
3. 重启计算机后再尝试

### 权限问题
如果遇到权限错误：
- Linux/macOS: 确保有写入用户目录的权限
- Windows: 以管理员身份运行命令提示符

### 清理后无法启动
如果清理后PAMS无法启动：
1. 检查用户数据目录的权限
2. 尝试手动删除整个用户数据目录
3. 重新安装PAMS

## 开发环境

在开发环境中，你还可以：

```bash
# 清理前端localStorage数据
# 在浏览器开发者工具中执行：
localStorage.clear()

# 清理IndexedDB数据
# 在浏览器开发者工具中的Application标签页删除相关数据库
```

## 备份建议

在清理数据前，建议备份重要数据：

```bash
# 备份数据库文件
cp ~/.config/pams/pams.db ~/backup/pams-backup-$(date +%Y%m%d).db

# 备份整个用户数据目录
cp -r ~/.config/pams ~/backup/pams-config-backup-$(date +%Y%m%d)
```

## 联系支持

如果在数据库清理过程中遇到问题，请：
1. 查看应用日志文件
2. 记录错误信息
3. 联系技术支持团队
