# PAMS 病原微生物分析管理系统 - 用户手册

## 系统概述

PAMS (Pathogen Analysis Management System) 是一个基于 Electron + Vue.js 开发的病原微生物分析管理系统，提供菌株管理、基因组分析、生物信息学分析等功能。

## 系统架构

- **前端**: Vue.js 3 + Element Plus
- **后端**: Electron + Node.js
- **数据库**: SQLite
- **开发语言**: JavaScript

## 功能模块

### 1. 用户管理

#### 1.1 用户登录
- 默认管理员账户：`admin` / `admin123`
- 支持用户名密码登录
- 登录状态保持

#### 1.2 用户权限管理
- 基于角色的权限控制系统 (RBAC)
- 预定义角色：
  - **系统管理员**: 拥有所有权限
  - **分析员**: 可进行生物信息学分析
  - **操作员**: 可管理菌株和基因组数据
  - **查看者**: 只能查看数据
- 支持自定义角色和权限分配

### 2. 菌株管理

#### 2.1 菌株信息管理
- **基本信息**：菌株编号、菌株名称、菌种、地区、样本来源
- **扩展信息**：实验类型、分离日期、描述信息
- **数据验证**：菌株编号唯一性检查
- **批量操作**：支持批量删除菌株

#### 2.2 菌株数据操作
- 新增菌株信息
- 编辑菌株信息
- 删除菌株（单个/批量）
- 菌株信息搜索和筛选
- 数据导入导出功能

### 3. 基因组管理

#### 3.1 基因组文件上传
- 支持 FASTA 格式文件上传
- 文件大小和格式验证
- 与菌株信息关联
- 浏览器环境模拟上传功能

#### 3.2 基因组数据管理
- 基因组文件列表查看
- 基因组信息编辑
- 基因组文件删除
- 基因组质量统计

### 4. 生物信息学分析

#### 4.1 MLST 分析
- **功能**：多位点序列分型分析
- **输入**：选择基因组文件
- **输出**：序列类型、等位基因信息、置信度
- **支持菌种**：大肠杆菌、沙门氏菌、金黄色葡萄球菌等

#### 4.2 血清分型分析
- **功能**：血清型预测
- **输入**：基因组序列
- **输出**：血清型、抗原信息、置信度
- **方法**：计算机预测 (in silico)

#### 4.3 毒力基因检测
- **功能**：毒力基因识别和注释
- **输出**：基因名称、基因家族、同源性、覆盖度
- **数据库**：内置毒力基因数据库

#### 4.4 耐药基因检测
- **功能**：耐药基因识别
- **输出**：基因名称、耐药类别、抗生素、耐药机制
- **应用**：抗生素敏感性预测

#### 4.5 分析任务管理
- 分析任务创建和跟踪
- 任务状态监控（待处理、运行中、已完成、失败）
- 分析结果存储和查看
- 分析历史记录

### 5. 系统设置

#### 5.1 实验相关设置

##### 5.1.1 菌种管理
- **基本信息**：菌种名称、学名、缩写
- **分类信息**：NCBI Taxonomy ID
- **功能**：添加、编辑、删除菌种
- **批量操作**：支持批量删除
- **数据同步**：与菌株管理菌种下拉菜单关联

##### 5.1.2 地区管理
- 地区名称管理
- 地区信息的增删改查
- 与菌株管理地区选择关联

##### 5.1.3 样本来源管理
- 样本来源类型管理
- 来源信息维护
- 与菌株管理样本来源选择关联

##### 5.1.4 实验类型管理
- 实验类型定义
- 实验类型的增删改查
- 与菌株管理实验类型选择关联

#### 5.2 系统相关设置

##### 5.2.1 数据库管理
- **健康检查**：数据库状态监控
- **性能优化**：数据库优化工具
- **迁移历史**：数据库版本管理
- **统计信息**：表完整性、索引状态、数据库大小

##### 5.2.2 权限管理
- **角色管理**：创建、编辑、删除角色
- **权限分配**：为角色分配具体权限
- **用户角色**：为用户分配角色
- **权限列表**：查看系统所有权限

### 6. 数据库功能

#### 6.1 数据库迁移
- 自动数据库版本管理
- 结构变更的平滑升级
- 迁移历史记录
- 回滚机制支持

#### 6.2 性能优化
- 自动索引创建
- 查询性能优化
- 数据库清理和压缩
- 统计信息更新

#### 6.3 数据完整性
- 外键约束定义
- 数据验证规则
- 事务处理支持
- 错误恢复机制

### 7. 安全功能

#### 7.1 输入验证
- 表单数据验证
- 文件类型检查
- 数据长度限制
- 特殊字符过滤

#### 7.2 权限控制
- 基于角色的访问控制
- 功能级权限验证
- 数据级权限控制
- 会话管理

#### 7.3 审计日志
- 用户操作记录
- 系统事件日志
- 错误日志记录
- 日志查询和分析

## 技术特性

### 1. 跨平台支持
- Windows、macOS、Linux 支持
- 桌面应用程序
- 本地数据存储

### 2. 模块化设计
- 服务层架构
- 功能模块分离
- 易于扩展和维护

### 3. 数据管理
- SQLite 本地数据库
- 数据导入导出
- 备份和恢复

### 4. 用户界面
- 响应式设计
- 现代化 UI 组件
- 直观的操作流程

## 系统要求

### 最低配置
- **操作系统**: Windows 10 / macOS 10.14 / Ubuntu 18.04
- **内存**: 4GB RAM
- **存储**: 2GB 可用空间
- **处理器**: Intel i3 或同等性能

### 推荐配置
- **操作系统**: Windows 11 / macOS 12 / Ubuntu 20.04
- **内存**: 8GB RAM
- **存储**: 10GB 可用空间
- **处理器**: Intel i5 或同等性能

## 安装和部署

### 开发环境
```bash
# 克隆项目
git clone <repository-url>

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 生产环境
```bash
# 构建应用
npm run build

# 打包桌面应用
npm run electron:build
```

## 使用指南

### 首次使用
1. 启动应用程序
2. 使用默认管理员账户登录 (admin/admin123)
3. 在系统设置中配置基础数据（菌种、地区等）
4. 开始添加菌株和基因组数据
5. 进行生物信息学分析

### 日常操作
1. **菌株管理**: 在菌株管理模块添加和维护菌株信息
2. **基因组上传**: 上传对应的基因组序列文件
3. **分析执行**: 选择基因组进行各类生物信息学分析
4. **结果查看**: 查看和导出分析结果
5. **数据维护**: 定期进行数据库优化和备份

## 故障排除

### 常见问题
1. **登录失败**: 检查用户名密码，确认数据库连接正常
2. **文件上传失败**: 检查文件格式和大小限制
3. **分析任务失败**: 查看错误日志，检查输入数据格式
4. **界面显示异常**: 清除浏览器缓存，重启应用

### 技术支持
- 查看系统日志获取详细错误信息
- 使用数据库健康检查功能诊断问题
- 联系技术支持团队获取帮助

## 更新日志

### v1.0.0 (当前版本)
- 实现基础菌株管理功能
- 添加基因组文件上传和管理
- 开发生物信息学分析模块
- 实现用户权限管理系统
- 添加数据库迁移和优化功能
- 完善系统设置和配置管理

---

*本文档将随着系统功能的更新而持续维护和完善。*
