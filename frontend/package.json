{"name": "pams-frontend", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@fortawesome/fontawesome-svg-core": "^7.0.0", "@fortawesome/free-solid-svg-icons": "^7.0.0", "@fortawesome/vue-fontawesome": "^3.1.0", "@icon-park/vue-next": "^1.4.2", "axios": "^1.5.0", "biojs-io-fasta": "^0.1.17", "echarts": "^5.4.3", "element-plus": "^2.3.14", "file-saver": "^2.0.5", "vue": "^3.3.4", "vue-echarts": "^6.6.1", "vue-i18n": "^11.1.10", "vue-router": "^4.2.4", "vuex": "^4.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/eslint-parser": "^7.28.0", "@vue/cli-plugin-eslint": "~5.0.8", "@vue/cli-plugin-router": "~5.0.8", "@vue/cli-plugin-vuex": "~5.0.8", "@vue/cli-service": "~5.0.8", "@vue/eslint-config-standard": "^8.0.1", "buffer": "^6.0.3", "eslint": "^8.49.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-n": "^16.1.0", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-vue": "^9.17.0", "node-loader": "^2.1.0", "sass-embedded": "^1.77.0", "sass-loader": "^14.0.0", "stream-browserify": "^3.0.0", "util": "^0.12.5"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}