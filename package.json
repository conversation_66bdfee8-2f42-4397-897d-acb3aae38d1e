{"name": "pams", "version": "0.1.0", "description": "实验室细菌基因组管理工具 - 基于Electron + Vue.js + Node.js的桌面应用", "main": "electron/main.js", "homepage": "./", "scripts": {"electron": "cross-env IS_ELECTRON=true electron .", "electron-dev": "concurrently \"npm run serve\" \"wait-on http://localhost:8080 && cross-env IS_ELECTRON=true DEV_PORT=8080 electron .\"", "electron-pack": "electron-builder", "serve": "cd frontend && npx vue-cli-service serve --host localhost", "build": "cd frontend && vue-cli-service build", "build:electron": "cd frontend && cross-env IS_ELECTRON=true npx vue-cli-service build", "lint": "cd frontend && vue-cli-service lint", "dev": "concurrently \"npm run serve\" \"wait-on http://localhost:8080 && cross-env IS_ELECTRON=true DEV_PORT=8080 electron .\"", "postinstall": "cd frontend && npm install && cd .. && electron-builder install-app-deps", "preelectron-pack": "npm run build:electron", "dist": "npm run build:electron && electron-builder --publish=never", "test": "jest", "rebuild": "echo 'No native modules to rebuild'", "clear-db": "node scripts/clear-database.js", "import-strains": "node cli/import-strains.js", "import:help": "node cli/import-strains.js --help"}, "keywords": ["bioinformatics", "genomics", "bacteria", "pathogen", "laboratory", "electron", "vue", "desktop"], "author": "PAMS Development Team", "license": "MIT", "devDependencies": {"concurrently": "^7.6.0", "cross-env": "^7.0.3", "electron": "^25.9.0", "electron-builder": "^24.6.4", "jest": "^29.7.0", "wait-on": "^7.0.1"}, "dependencies": {"axios": "^1.5.0", "bcryptjs": "^2.4.3", "chalk": "^4.1.2", "commander": "^9.4.1", "cross-spawn": "^7.0.3", "csv-parser": "^3.0.0", "electron-store": "^8.1.0", "express": "^4.18.2", "fs-extra": "^11.1.1", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.2", "progress": "^2.0.3", "sql.js": "^1.8.0", "uuid": "^9.0.1", "vue-i18n": "^11.1.10"}, "build": {"appId": "com.pams.app", "productName": "PAMS", "directories": {"output": "dist"}, "files": ["frontend/dist/**/*", "electron/**/*", "src/**/*", "node_modules/**/*", "package.json"], "extraResources": [{"from": "data", "to": "data", "filter": ["**/*"]}], "mac": {"category": "public.app-category.developer-tools", "icon": "frontend/public/icon.icns"}, "win": {"target": "nsis", "icon": "frontend/public/icon.ico"}, "linux": {"target": "AppImage", "icon": "frontend/public/icon.png"}}}